import numpy as np
import torch
import time  # 添加time模块用于计时
from torch.utils.data import DataLoader
from FMLP import FMLP
from dataset import ClientsSampler, ClientsDataset, TestDataset, data_partition, evaluate, evaluate_valid
from metric import NDCG_binary_at_k_batch, AUC_at_k_batch, HR_at_k_batch


class Clients:
    """
    联邦学习客户端类
    主要功能：
    1. 实现真正的联邦学习 - 每个客户端独立训练
    2. 保护数据隐私 - 不混合多个客户端数据
    3. 返回梯度用于服务器聚合
    4. 预计算负样本，减少训练时的计算开销
    5. 隐私保护 - 隐私参数本地更新，非隐私参数服务器聚合
    """

    def __init__ ( self, config, logger ):
        """
        初始化客户端

        参数:
        - config: 包含各种配置参数的字典
        - logger: 日志记录器
        """
        # 负样本数量，通常为batch_size - 1
        self.neg_num = config['neg_num']
        self.logger = logger
        self.config = config

        # 数据路径
        self.data_path = config['datapath'] + config['dataset'] + '/' + config['train_data']

        # 序列最大长度
        self.maxlen = config.get('max_seq_len', 200)
        self.batch_size = config['batch_size']

        # 加载客户端数据集 - 使用真正的联邦学习数据集
        self.clients_data = ClientsDataset(self.data_path, self.neg_num, maxlen=self.maxlen)

        # 获取数据集信息
        self.dataset = self.clients_data.get_dataset()
        self.user_train, self.user_valid, self.user_test, self.usernum, self.itemnum = self.dataset

        # 设备选择：优先使用GPU
        self.device = "cuda" if torch.cuda.is_available () else "cpu"

        # 初始化FMLP模型
        self.model = FMLP (config, self.clients_data.get_maxid ())
        self.model.to (self.device)

        # 定义优化器 (Adam)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config['lr'],
                                         betas=(0.9, 0.98), weight_decay=config.get('l2_reg', 0.0))

        # 参数上传模式配置
        self.upload_mode = config.get('upload_mode', 'partial')

        # 记录参数名称，根据上传模式区分隐私和非隐私参数
        self.private_params = []  # 隐私参数名（本地更新）
        self.non_private_params = []  # 非隐私参数名（服务器聚合）

        self._configure_param_upload_mode()

        # 动态负采样配置
        self.use_dynamic_sampling = config.get('use_dynamic_sampling',True)  # 启用动态负采样

        self.logger.info(f"参数上传模式: {self.upload_mode}")
        self.logger.info(f"隐私参数（本地更新）: {self.private_params}")
        self.logger.info(f"非隐私参数（服务器聚合）: {self.non_private_params}")
        self.logger.info(f"动态负采样: {self.use_dynamic_sampling}")

    def random_neq(self, l, r, s):
        """
        生成不在集合s中的随机数，参考SAS.torch的random_neq函数

        参数:
        - l: 下界
        - r: 上界
        - s: 排除的集合

        返回:
        - t: 不在集合s中的随机数
        """
        t = np.random.randint(l, r)
        while t in s:
            t = np.random.randint(l, r)
        return t

    def dynamic_negative_sampling(self, user_id, input_len):
        """
        动态负采样函数，完全参考SAS.torch的采样逻辑
        为每个位置生成多个负样本，但采用SAS.torch的简单随机策略

        参数:
        - user_id: 用户ID
        - input_len: 输入序列长度

        返回:
        - neg_seq: 动态生成的负样本序列，形状为 (maxlen, neg_num)
        """
        # 获取用户交互过的物品集合
        user_items = set(self.clients_data.seq[user_id])

        # 获取输入序列和目标序列
        input_seq = self.clients_data.train_seq[user_id]
        target_seq = self.clients_data.valid_seq[user_id]
        maxlen = input_seq.shape[0]

        # 初始化负样本序列
        neg_seq = np.zeros((maxlen, self.neg_num), dtype=np.int32)

        # 为序列中每个有效位置生成负样本
        # 参考SAS.torch的逻辑：只为非填充位置生成负样本
        for pos in range(maxlen):
            if target_seq[pos] != 0:  # 只为非填充位置生成负样本
                for neg_idx in range(self.neg_num):
                    # 使用SAS.torch的简单随机策略：random_neq
                    neg_item = self.random_neq(1, self.clients_data.itemnum + 1, user_items)
                    neg_seq[pos, neg_idx] = neg_item

        return neg_seq

    def _configure_param_upload_mode(self):
        """根据上传模式配置参数分类"""
        for name, param in self.model.named_parameters():
            if self.upload_mode == 'full':
                # 全部上传模式：所有参数都上传到服务器，无隐私参数本地训练
                self.non_private_params.append(name)
            elif self.upload_mode == 'partial':
                # 部分上传模式：仅embedding参数上传，其他参数本地训练
                if "embedding" in name:
                    self.non_private_params.append(name)
                else:
                    self.private_params.append(name)

    def load_server_model_params ( self, model_param_state_dict ):
        """
        从服务器加载模型参数，根据上传模式更新相应参数

        参数:
        - model_param_state_dict: 从服务器接收的模型参数
        """
        # 获取当前模型的状态字典
        current_state = self.model.state_dict ()

        # 根据上传模式更新参数
        if self.upload_mode == 'full':
            # 全部上传模式：更新所有从服务器接收的参数
            for name, param in model_param_state_dict.items():
                if name in current_state:
                    current_state[name] = param
        else:
            # 部分上传模式：只更新非隐私参数（embedding参数）
            for name, param in model_param_state_dict.items ():
                if name in self.non_private_params:
                    current_state [name] = param

        # 加载更新后的状态字典
        self.model.load_state_dict (current_state)

    def train(self, uids, model_param_state_dict, epoch=0):
        """
        联邦学习训练方法 - 动态负采样版本
        实现标准的联邦学习流程：
        1. 每个客户端独立训练，保护数据隐私
        2. 动态生成负样本，提高学习效果
        3. 分离隐私梯度（不含embedding）和非隐私梯度（含embedding）
        4. 隐私梯度在本地更新，非隐私梯度上传服务器
        5. 返回所有客户端的非隐私梯度字典用于服务器聚合

        参数:
        - uids: 当前批次需要训练的客户端ID列表
        - model_param_state_dict: 从服务器接收的最新全局模型参数
        - epoch: 当前训练轮次，用于动态采样策略调整

        返回:
        - clients_grads: 所有客户端的非隐私梯度字典 {uid: {param_name: grad}}
        - clients_losses: 所有客户端的损失值字典 {uid: loss}
        """
        # 从服务器加载全局模型参数（只更新非隐私参数）
        self.load_server_model_params(model_param_state_dict)

        # 存储每个客户端计算出的非隐私梯度（仅embedding参数）
        clients_grads = {}
        # 存储每个客户端的损失值
        clients_losses = {}

        # 切换到训练模式
        self.model.train()

        # 每个客户端独立训练，不混合数据
        for uid in uids:
            uid = uid.item()  # 转换为标量

            # 获取该用户的基础数据
            input_seq = self.clients_data.train_seq[uid]
            target_seq = self.clients_data.valid_seq[uid]
            input_len = self.clients_data.seq_len[uid]

            # 动态生成负样本（参考SAS.torch策略）
            if self.use_dynamic_sampling:
                neg_seq = self.dynamic_negative_sampling(uid, input_len)
            else:
                # 回退到原始的静态采样方式（如果需要）
                cand = np.setdiff1d(self.clients_data.item_set, self.clients_data.seq[uid])
                prob = self.clients_data.item_prob[cand]
                prob = prob / prob.sum()
                neg_seq = np.random.choice(cand, (input_len, self.neg_num), p=prob)
                neg_seq = np.pad(neg_seq, ((input_seq.shape[0] - input_len, 0), (0, 0)))

            # 数据格式转换并移动到指定设备
            input_seq = torch.from_numpy(input_seq).unsqueeze(0).to(self.device)
            target_seq = torch.from_numpy(target_seq).unsqueeze(0).to(self.device)
            neg_seq = torch.from_numpy(neg_seq).unsqueeze(0).to(self.device)
            input_len = torch.tensor(input_len).unsqueeze(0).to(self.device)

            # 处理序列长度限制
            max_seq_length = self.model.max_seq_length
            if input_seq.size(1) > max_seq_length:
                # 保留序列的后半部分（最近的交互）
                input_seq = input_seq[:, -max_seq_length:]
                target_seq = target_seq[:, -max_seq_length:]
                neg_seq = neg_seq[:, -max_seq_length:, :]
                input_len = torch.clamp(input_len, max=max_seq_length)

            # 单个客户端前向传播
            seq_out = self.model(input_seq, input_len)

            # 创建填充位置的掩码
            padding_mask = (torch.not_equal(input_seq, 0)).float().unsqueeze(-1).to(self.device)

            # 计算损失
            loss = self.model.loss_function(seq_out, padding_mask, target_seq, neg_seq, input_len)

            # 保存损失值
            clients_losses[uid] = loss.item()

            # 反向传播计算梯度
            self.optimizer.zero_grad()
            loss.backward()

            # 分离隐私梯度和非隐私梯度
            non_private_grads = {}  # 非隐私梯度（含embedding的参数）

            if self.upload_mode == 'full':
                # 全部上传模式：收集所有梯度上传到服务器
                for name, param in self.model.named_parameters():
                    if param.grad is not None:
                        non_private_grads[name] = param.grad.clone()
                # 全部上传模式下不进行本地更新，等待服务器聚合后的参数
                self.optimizer.zero_grad()
            else:
                # 部分上传模式：仅收集embedding梯度上传，其他参数本地更新
                for name, param in self.model.named_parameters():
                    if param.grad is not None and name in self.non_private_params:
                        non_private_grads[name] = param.grad.clone()

                # 处理本地更新：清除上传参数的梯度，保留隐私参数的梯度
                for name, param in self.model.named_parameters():
                    if param.grad is not None and name in self.non_private_params:
                        param.grad = None  # 清除上传参数的梯度，不在本地更新

                # 本地更新隐私参数
                self.optimizer.step()

            # 存储该客户端的非隐私梯度
            clients_grads[uid] = non_private_grads

        return clients_grads, clients_losses


class Server:
    """
    联邦学习服务器类
    主要功能：
    1. 实现FedAvg梯度聚合算法
    2. 处理多个客户端的梯度平均
    3. 维护全局模型并进行评估
    """

    def __init__ ( self, config, clients, logger ):
        """
        初始化服务器

        参数:
        - config: 配置参数字典
        - clients: 客户端对象
        - logger: 日志记录器
        """
        # 保存客户端引用
        self.clients = clients
        self.config = config

        # 训练参数
        self.batch_size = config ['batch_size']
        self.epochs = config ['epochs']
        self.early_stop = config ['early_stop']
        self.maxlen = config['max_seq_len']
        self.skip_test_eval=config['skip_test_eval']
        self.eval_freq=config['eval_freq']

        self.early_stop_enabled = config['early_stop_enabled'] #是否启用早停

        # 设备选择
        self.device = "cuda" if torch.cuda.is_available () else "cpu"

        # 日志记录器
        self.logger = logger

        # 获取数据集信息
        self.dataset = self.clients.dataset

        # 初始化服务器端的全局模型
        self.model = FMLP (config, self.clients.clients_data.get_maxid ())
        self.model.to (self.device)

        # 定义服务器端的优化器 (Adam，与pmixer保持一致)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config['lr'],
                                         betas=(0.9, 0.98), weight_decay=config.get('l2_reg', 0.0))

    def aggregate_gradients ( self, clients_grads ):
        """
        实现FedAvg梯度聚合算法 - 隐私保护版本
        只聚合非隐私梯度（含embedding的参数）

        参数:
        - clients_grads: 从客户端收集的非隐私梯度字典 {uid: {param_name: grad}}
        """
        clients_num = len (clients_grads)
        aggregated_gradients = {}

        # FedAvg梯度平均聚合 - 只处理非隐私梯度
        for uid, grads_dict in clients_grads.items ():
            for name, grad in grads_dict.items ():
                if name in aggregated_gradients:
                    aggregated_gradients [name] += grad / clients_num
                else:
                    aggregated_gradients [name] = grad / clients_num

        # 将聚合后的梯度应用到服务器的全局模型上
        # 只更新非隐私参数（含embedding的参数）
        for name, param in self.model.named_parameters ():
            if name in aggregated_gradients:
                # 应用聚合后的平均梯度（仅对embedding参数）
                param.grad = aggregated_gradients [name]
            else:
                # 隐私参数（不含embedding）：梯度设为None，不在服务器端更新
                param.grad = None

    def evaluate ( self, data_loader ):
        """
        通用评估方法，用于在测试集或验证集上评估模型性能

        参数:
        - data_loader: 数据加载器 (测试集或验证集)

        返回:
        - ndcg10: NDCG@10指标
        - hr10: HR@10指标
        - auc: AUC指标
        - eval_time: 评估用时
        """
        # 记录评估开始时间 1
        start_time = time.time ()

        # 切换模型到评估模式
        self.model.eval ()

        # 初始化评估指标列表
        ndcg10_list, hr10_list, auc_list = [], [], []

        # 在评估过程中不计算梯度，节省内存和计算时间
        with torch.no_grad ():
            for input_seq, input_len, train_vec, target_vec in data_loader:
                # 将数据移动到指定设备
                input_seq = input_seq.to (self.device)
                input_len = input_len.to (self.device)

                # 处理序列长度限制
                max_seq_length = self.model.max_seq_length
                if input_seq.size (1) > max_seq_length:
                    # 保留序列后部分（最近交互）
                    input_seq = input_seq [:, -max_seq_length:]
                    input_len = torch.clamp (input_len, max = max_seq_length)

                # 获取模型预测
                pro = self.model (input_seq, input_len)
                recon_batch = []

                # 提取每个序列最后一个时间步的预测结果
                for i in range (input_seq.shape [0]):
                    # 对于前填充序列，计算最后一个有效位置
                    last_idx = min (input_seq.shape [1] - 1, input_len [i].item () - 1)
                    scores = pro [i, last_idx, :].unsqueeze (0)
                    recon_batch.append (scores)

                # 合并所有预测结果
                recon_batch = torch.cat (recon_batch)
                recon_batch = recon_batch.detach ().cpu ().numpy ()

                # 准备评估用的真实数据
                train_vec = train_vec.numpy ()
                target_vec = target_vec.numpy ()

                # 过滤掉用户已经交互过的物品
                # 将已交互物品的分数设为负无穷，确保不会被推荐
                recon_batch [train_vec.nonzero ()] = -np.inf

                # 计算各种评估指标
                n_10 = NDCG_binary_at_k_batch (recon_batch [:, 1:], target_vec [:, 1:], 10)
                auc_b = AUC_at_k_batch (train_vec [:, 1:], recon_batch [:, 1:], target_vec [:, 1:])
                hr_10 = HR_at_k_batch (recon_batch [:, 1:], target_vec [:, 1:], 10)

                # 收集评估结果
                auc_list.append (auc_b)
                ndcg10_list.append (n_10)
                hr10_list.append (hr_10)

        # 计算各指标的平均值
        auc = np.mean (np.concatenate (auc_list))
        ndcg10 = np.mean (np.concatenate (ndcg10_list))
        hr10 = np.mean (hr10_list)

        # 计算总评估用时
        eval_time = time.time () - start_time

        return ndcg10, hr10, auc, eval_time

    def train ( self ):
        """
        真正的联邦学习训练循环
        """
        # 创建客户端批次序列
        user_set = self.clients.clients_data.get_user_set()
        uid_seq = []
        for i in range(0, len(user_set), self.batch_size):
            batch_uids = user_set[i:i + self.batch_size]
            uid_seq.append(torch.tensor(batch_uids))

        # 早停相关变量
        best_val_ndcg, best_val_hr = 0.0, 0.0
        best_test_ndcg, best_test_hr = 0.0, 0.0

        # 早停计数器 - 记录连续没有NDCG改善的轮数
        no_improve_count = 0

        # 初始化模型参数
        for name, param in self.model.named_parameters():
            try:
                torch.nn.init.xavier_normal_(param.data)
            except:
                pass  # 忽略初始化失败的层

        # 设置embedding层的padding位置为0
        if hasattr(self.model, 'position_embedding'):
            self.model.position_embedding.weight.data[0, :] = 0
        if hasattr(self.model, 'item_embedding'):
            self.model.item_embedding.weight.data[0, :] = 0

        T = 0.0
        t0 = time.time()

        # 开始多轮训练
        early_stop_triggered = False  # 添加早停标志
        for epoch in range(self.epochs):
            # 记录epoch开始时间
            epoch_start_time = time.time()

            # 切换模型到训练模式
            self.model.train()

            # 训练统计变量
            batch_count = 0
            epoch_losses = []

            # 遍历所有客户端批次
            for uids in uid_seq:
                # 清零梯度
                self.optimizer.zero_grad()

                # 获取所有客户端的梯度字典和损失值字典
                clients_grads, clients_losses = self.clients.train(uids, self.model.state_dict(), epoch)

                # 收集本批次的损失值
                batch_losses = list(clients_losses.values())
                epoch_losses.extend(batch_losses)

                # 使用FedAvg梯度聚合方法
                self.aggregate_gradients(clients_grads)

                # 更新全局模型参数
                self.optimizer.step()

                # 记录批次统计
                batch_count += 1

            eval_freq = self.eval_freq
            # 在第一个epoch、每eval_freq个epoch或最后一个epoch进行评估
            should_evaluate = (epoch + 1) % eval_freq == 0 or epoch == 0 or epoch == self.epochs - 1
            if should_evaluate:
                self.model.eval()
                #t1是该轮训练时间 T为总时间
                t1 = time.time() - t0
                T += t1
                print('Evaluating', end='')

                # 验证集评估（始终进行）
                t_valid = evaluate_valid(self.model, self.dataset, self.maxlen, self.device)

                # 早停检查 - 检查NDCG是否有所改善
                if self.early_stop_enabled:
                    if t_valid[0] > best_val_ndcg:
                        # NDCG有改善，重置计数器
                        no_improve_count = 0
                        best_val_ndcg = t_valid[0]
                    else:
                        # NDCG没有改善，增加计数器
                        no_improve_count += 1

                    # 如果连续多轮没有改善，触发早停
                    if no_improve_count >= self.early_stop:
                        self.logger.info(f"早停触发！NDCG在{self.early_stop}轮内没有改善。")
                        early_stop_triggered = True

                # 测试集评估（可选）
                if not self.skip_test_eval:
                    t_test = evaluate(self.model, self.dataset, self.maxlen, self.device)

                  
                    # 记录到日志
                    self.logger.info('epoch:%d, time: %f(s), valid (NDCG@10: %.4f, HR@10: %.4f), test (NDCG@10: %.4f, HR@10: %.4f) all_time: %f(s)'
                            % (epoch + 1, t1, t_valid[0], t_valid[1], t_test[0], t_test[1], T))
                else:
                    # 跳过测试集评估，设置默认值
                    t_test = (0.0, 0.0)
                    # 记录到日志
                    self.logger.info('epoch:%d, time: %f(s), valid (NDCG@10: %.4f, HR@10: %.4f), test: SKIPPED, all_time: %f(s)'
                            % (epoch + 1, t1, t_valid[0], t_valid[1],T))

                # 更新最佳结果
                if not self.skip_test_eval:
                    # 包含测试集评估的情况
                    if t_valid[0] > best_val_ndcg or t_valid[1] > best_val_hr or t_test[0] > best_test_ndcg or t_test[1] > best_test_hr:
                        best_val_ndcg = max(t_valid[0], best_val_ndcg)
                        best_val_hr = max(t_valid[1], best_val_hr)
                        best_test_ndcg = max(t_test[0], best_test_ndcg)
                        best_test_hr = max(t_test[1], best_test_hr)
                        self.logger.info(f"新的最佳性能: valid NDCG@10={best_val_ndcg:.4f}, test NDCG@10={best_test_ndcg:.4f}")

                else:
                    # 跳过测试集评估的情况，只基于验证集更新
                    if t_valid[0] > best_val_ndcg or t_valid[1] > best_val_hr:
                        best_val_ndcg = max(t_valid[0], best_val_ndcg)
                        best_val_hr = max(t_valid[1], best_val_hr)
                        self.logger.info(f"新的最佳性能: valid NDCG@10={best_val_ndcg:.4f}, valid HR@10={best_val_hr:.4f}")

                t0 = time.time()
                self.model.train()

                # 在评估完成后检查早停标志
                if early_stop_triggered:
                    break

            # 如果早停被触发，跳出训练循环
            if early_stop_triggered:
                break

        # 记录最佳结果
        if not self.skip_test_eval:
            self.logger.info('[联邦训练] 最佳结果: valid NDCG@10={:.4f}, HR@10={:.4f}, test NDCG@10={:.4f}, HR@10={:.4f}'.format(
                best_val_ndcg, best_val_hr, best_test_ndcg, best_test_hr))
        else:
            self.logger.info('[联邦训练] 最佳结果: valid NDCG@10={:.4f}, HR@10={:.4f} (测试集评估已跳过)'.format(
                best_val_ndcg, best_val_hr))