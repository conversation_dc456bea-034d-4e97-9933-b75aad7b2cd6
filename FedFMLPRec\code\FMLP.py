import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import copy
import numpy as np


class GeLu(nn.Module):
    def __init__(self):
        super(GeLu, self).__init__()

    def forward(self, x):
        return x * 0.5 * (1.0 + torch.erf(x / math.sqrt(2.0)))
    

class Swish(nn.Module):
    def __init__(self):
        super(Swish, self).__init__()

    def forward(self, x):
        return x * torch.sigmoid(x)


ACT2FN = {"gelu": GeLu(), "relu": nn.ReLU(), "swish": Swish()}


class LayerNorm(nn.Module):
    def __init__(self, hidden_size, eps=1e-12):
        super(LayerNorm, self).__init__()
        self.weight = nn.Parameter(torch.ones(hidden_size))
        self.bias = nn.Parameter(torch.zeros(hidden_size))
        self.variance_epsilon = eps

    def forward(self, x):
        u = x.mean(-1, keepdim=True)
        s = (x - u).pow(2).mean(-1, keepdim=True)
        x = (x - u) / torch.sqrt(s + self.variance_epsilon)
        return self.weight * x + self.bias
    

class FilterLayer(nn.Module):
    def __init__(self, max_seq_length, hidden_size, hidden_dropout_prob=0.5):
        super(FilterLayer, self).__init__()
        self.complex_weight = nn.Parameter(
            torch.randn(1, max_seq_length // 2 + 1, hidden_size, 2, dtype=torch.float32) * 0.02
        )
        self.out_dropout = nn.Dropout(hidden_dropout_prob)
        self.layernorm = LayerNorm(hidden_size, eps=1e-12)

    def forward(self, input_tensor):
        # [batch, seq_len, hidden]
        batch, seq_len, hidden = input_tensor.shape
        x = torch.fft.rfft(input_tensor, dim=1, norm='ortho')
        weight = torch.view_as_complex(self.complex_weight)
        x = x * weight
        sequence_emb_fft = torch.fft.irfft(x, n=seq_len, dim=1, norm='ortho')
        hidden_states = self.out_dropout(sequence_emb_fft)
        hidden_states = self.layernorm(hidden_states + input_tensor)

        return hidden_states


class Intermediate(nn.Module):
    def __init__(self, hidden_size, hidden_act='gelu', hidden_dropout_prob=0.5):
        super(Intermediate, self).__init__()
        self.dense_1 = nn.Linear(hidden_size, hidden_size * 4)
        assert isinstance(hidden_act, str), 'invalid hidden acivation'
        self.intermediate_act_fn = ACT2FN[hidden_act]

        self.dense_2 = nn.Linear(4 * hidden_size, hidden_size)
        self.layernorm = LayerNorm(hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(hidden_dropout_prob)

    def forward(self, input_tensor):
        hidden_states = self.dense_1(input_tensor)
        hidden_states = self.intermediate_act_fn(hidden_states)

        hidden_states = self.dense_2(hidden_states)
        hidden_states = self.dropout(hidden_states)
        hidden_states = self.layernorm(hidden_states + input_tensor)

        return hidden_states


class Layer(nn.Module):
    def __init__(self, max_seq_length, hidden_size, hidden_dropout_prob, hidden_act):
        super(Layer, self).__init__()
        self.filterlayer = FilterLayer(max_seq_length, hidden_size, hidden_dropout_prob)
        self.intermediate = Intermediate(hidden_size, hidden_act, hidden_dropout_prob)

    def forward(self, hidden_states):
        hidden_states = self.filterlayer(hidden_states)
        intermediate_output = self.intermediate(hidden_states)

        return intermediate_output


class Encoder(nn.Module):
    def __init__(self, n_layers, max_seq_length, hidden_size, hidden_dropout_prob, hidden_act):
        super(Encoder, self).__init__()
        layer = Layer(max_seq_length, hidden_size, hidden_dropout_prob, hidden_act)
        self.layer = nn.ModuleList([copy.deepcopy(layer)
                                    for _ in range(n_layers)])

    def forward(self, hidden_states, output_all_encoded_layers=True):
        all_encoder_layers = []
        for layer_module in self.layer:
            hidden_states = layer_module(hidden_states)
            if output_all_encoded_layers:
                all_encoder_layers.append(hidden_states)
        if not output_all_encoded_layers:
            all_encoder_layers.append(hidden_states)
        return all_encoder_layers


class FMLP(nn.Module):
    """
    FMLP 模型类，基于频域多层感知机的序列推荐模型。
    兼容SASRec的接口，可以直接替换使用。
    """

    def __init__(self, config, item_maxid):
        super(FMLP, self).__init__()

        # 模型配置参数 - 兼容SASRec的配置
        self.n_layers = config['num_layers']
        
        # 使用embed_dim作为hidden_size，与SASRec保持一致
        self.embed_dim = config['embed_dim']
        self.hidden_size = self.embed_dim
        
        self.hidden_dropout_prob = config['dropout']
        self.hidden_act = config.get('hidden_act', 'gelu')  # FMLP特有参数
        self.layer_norm_eps = 1e-12
        self.max_seq_length = config['max_seq_len']

        # 设备配置
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # 嵌入层 - 与SASRec保持一致
        self.n_items = item_maxid + 1
        self.item_embedding = nn.Embedding(self.n_items, self.hidden_size, padding_idx=0)
        self.position_embedding = nn.Embedding(self.max_seq_length, self.hidden_size)

        # LayerNorm和Dropout
        self.layernorm = LayerNorm(self.hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(self.hidden_dropout_prob)
        self.emb_dropout = nn.Dropout(self.hidden_dropout_prob)  # 兼容SASRec

        # FMLP编码器
        self.item_encoder = Encoder(
            n_layers=self.n_layers,
            max_seq_length=self.max_seq_length,
            hidden_size=self.hidden_size,
            hidden_dropout_prob=self.hidden_dropout_prob, 
            hidden_act=self.hidden_act,
        )

        # 初始化参数
        self.init_param()

    def init_param(self):
        """初始化模型参数 - 与SASRec保持一致"""
        # 使用xavier_normal_初始化所有参数
        for name, param in self.named_parameters():
            try:
                nn.init.xavier_normal_(param.data)
            except:
                pass  # 忽略初始化失败的层

        # 设置padding位置的embedding为0
        self.position_embedding.weight.data[0, :] = 0
        self.item_embedding.weight.data[0, :] = 0

    def gather_indexes(self, output, gather_index):
        """从输出中收集指定索引的特征"""
        gather_index = gather_index.view(-1, 1, 1).expand(-1, -1, output.shape[-1])
        output_tensor = output.gather(dim=1, index=gather_index)
        return output_tensor.squeeze(1)

    def add_position_embedding(self, sequence):
        """添加位置嵌入"""
        seq_length = sequence.size(1) 
        position_ids = torch.arange(seq_length, dtype=torch.long, device=sequence.device)
        position_ids = position_ids.unsqueeze(0).expand_as(sequence)
        item_embeddings = self.item_embedding(sequence)
        position_embeddings = self.position_embedding(position_ids)
        sequence_emb = item_embeddings + position_embeddings
        sequence_emb = self.layernorm(sequence_emb)
        sequence_emb = self.dropout(sequence_emb)

        return sequence_emb

    def forward(self, x, x_lens):
        """
        定义模型的前向传播过程，兼容SASRec接口。

        参数:
        - x: 输入的物品序列 (batch_size, seq_len)。
        - x_lens: 每个序列的实际长度 (batch_size)。

        返回:
        - seq_out: 模型对序列中每个位置的下一个物品的预测得分。
        """
        item_seq = x

        # 确保序列长度不超过模型定义的最大长度
        if item_seq.size(1) > self.max_seq_length:
            item_seq = item_seq[:, -self.max_seq_length:]
            x_lens = torch.clamp(x_lens, max=self.max_seq_length)

        # 确保item_seq中的ID不超出范围
        item_seq = torch.clamp(item_seq, 0, self.n_items - 1)

        # 添加位置嵌入
        sequence_emb = self.add_position_embedding(item_seq)

        # FMLP编码
        item_encoded_layers = self.item_encoder(
            sequence_emb,
            output_all_encoded_layers=True)

        output = item_encoded_layers[-1]

        # 计算每个物品的预测得分
        seq_output = torch.matmul(output, self.item_embedding.weight.transpose(0, 1))

        return seq_output

    def loss_function(self, seq_out, padding_mask, target, neg, seq_len):
        """
        计算模型的损失函数，使用二元交叉熵(Binary Cross Entropy)损失
        兼容SASRec的loss_function接口

        参数:
        - seq_out: 模型的输出得分。
        - padding_mask: 用于忽略填充位置的掩码。
        - target: 目标物品ID (正样本)。
        - neg: 负采样物品序列 (负样本)。
        - seq_len: 序列长度 (未使用，保留接口兼容性)。

        返回:
        - loss: 计算出的损失值。
        """
        # 提取目标物品(正样本)的预测分数
        target_output = torch.gather(seq_out, 2, target.unsqueeze(-1).long())

        # 使用二元交叉熵损失
        # 处理负样本，可能是多个负样本
        if neg.dim() == 3:
            # 多个负样本的情况
            neg_output = torch.gather(seq_out, 2, neg.long())

            # 计算正样本损失：-log(sigmoid(pos_score))
            pos_loss = -torch.log(torch.sigmoid(target_output))

            # 计算多个负样本的损失并平均：-log(1-sigmoid(neg_score))
            neg_loss = -torch.log(1 - torch.sigmoid(neg_output)).mean(dim=-1, keepdim=True)
        else:
            # 单个负样本的情况
            neg_output = torch.gather(seq_out, 2, neg.unsqueeze(-1).long())

            # 计算正样本损失
            pos_loss = -torch.log(torch.sigmoid(target_output))

            # 计算负样本损失
            neg_loss = -torch.log(1 - torch.sigmoid(neg_output))

        # 合并正负样本损失
        loss = pos_loss + neg_loss

        # 应用掩码，确保只计算非填充位置的损失
        loss = loss * padding_mask.unsqueeze(-1)

        # 计算平均损失 (只考虑非padding位置)
        non_zero_elements = padding_mask.sum()
        if non_zero_elements > 0:
            loss = loss.sum() / non_zero_elements
        else:
            loss = loss.sum()

        return loss

    def log2feats(self, log_seqs):
        """
        将序列转换为特征表示，兼容SASRec的log2feats方法

        参数:
        - log_seqs: 输入序列 (batch_size, seq_len)

        返回:
        - log_feats: 序列特征表示 (batch_size, seq_len, hidden_size)
        """
        # 转换为tensor
        if not isinstance(log_seqs, torch.Tensor):
            log_seqs = torch.LongTensor(log_seqs).to(self.device)

        # 物品嵌入
        seqs = self.item_embedding(log_seqs)
        seqs *= self.item_embedding.embedding_dim ** 0.5

        # 位置嵌入 - 与SASRec保持一致的方式
        poss = np.tile(np.arange(1, log_seqs.shape[1] + 1), [log_seqs.shape[0], 1])
        poss *= (log_seqs.cpu().numpy() != 0)  # 只对非padding位置添加位置编码
        seqs += self.position_embedding(torch.LongTensor(poss).to(self.device))
        seqs = self.emb_dropout(seqs)  # 使用embedding dropout

        # FMLP编码
        item_encoded_layers = self.item_encoder(
            seqs, output_all_encoded_layers=True
        )

        log_feats = self.layernorm(item_encoded_layers[-1])

        return log_feats

    def forward_pmixer(self, user_ids, log_seqs, pos_seqs, neg_seqs):
        """
        基于pmixer的forward方法，用于训练，兼容SASRec接口

        参数:
        - user_ids: 用户ID (batch_size,)
        - log_seqs: 输入序列 (batch_size, seq_len)
        - pos_seqs: 正样本序列 (batch_size, seq_len)
        - neg_seqs: 负样本序列 (batch_size, seq_len)

        返回:
        - pos_logits: 正样本的预测得分
        - neg_logits: 负样本的预测得分
        """
        log_feats = self.log2feats(log_seqs)  # user_ids暂时未使用

        # 转换为tensor
        if not isinstance(pos_seqs, torch.Tensor):
            pos_seqs = torch.LongTensor(pos_seqs).to(self.device)
        if not isinstance(neg_seqs, torch.Tensor):
            neg_seqs = torch.LongTensor(neg_seqs).to(self.device)

        pos_embs = self.item_embedding(pos_seqs)
        neg_embs = self.item_embedding(neg_seqs)

        pos_logits = (log_feats * pos_embs).sum(dim=-1)
        neg_logits = (log_feats * neg_embs).sum(dim=-1)

        return pos_logits, neg_logits

    def predict(self, user_ids, log_seqs, item_indices):
        """
        基于pmixer的predict方法，用于推理，兼容SASRec接口

        参数:
        - user_ids: 用户ID (batch_size,)
        - log_seqs: 输入序列 (batch_size, seq_len)
        - item_indices: 候选物品ID列表 (num_items,)

        返回:
        - logits: 预测得分 (batch_size, num_items)
        """
        log_feats = self.log2feats(log_seqs)  # user_ids暂时未使用

        final_feat = log_feats[:, -1, :]  # 只使用最后一个位置的特征

        # 转换为tensor
        if not isinstance(item_indices, torch.Tensor):
            item_indices = torch.LongTensor(item_indices).to(self.device)

        item_embs = self.item_embedding(item_indices)  # (num_items, hidden_size)
        logits = item_embs.matmul(final_feat.unsqueeze(-1)).squeeze(-1)  # (batch_size, num_items)

        return logits

    def get_attention_mask(self, item_seq, bidirectional=False):
        """
        生成注意力掩码，兼容SASRec接口
        注意：FMLP不使用注意力机制，但保留此方法以保持接口兼容性
        """
        # FMLP不需要注意力掩码，返回None或者简单的掩码
        return None
